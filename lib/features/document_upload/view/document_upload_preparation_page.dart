import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/hive/hive_config.dart';
import 'package:paperless_mobile/core/database/tables/global_settings.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/repository/label_repository.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/core/widgets/form_builder_fields/form_builder_localized_date_picker.dart';
import 'package:paperless_mobile/core/widgets/future_or_builder.dart';
import 'package:paperless_mobile/features/document_bulk_action/view/widgets/fullscreen_bulk_edit_tags_widget.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';
import 'package:paperless_mobile/features/document_upload/view/permissions_page.dart';
import 'package:paperless_mobile/features/document_upload/view/project_page.dart';
import 'package:paperless_mobile/features/labels/tags/view/widgets/tags_form_field.dart';
import 'package:paperless_mobile/features/labels/view/pages/labels_page.dart';
import 'package:paperless_mobile/features/labels/view/widgets/fullscreen_label_form.dart';
import 'package:paperless_mobile/features/labels/view/widgets/label_form_field.dart';
import 'package:paperless_mobile/features/linked_documents/view/linked_documents_page.dart';
import 'package:paperless_mobile/features/logging/data/logger.dart';
import 'package:paperless_mobile/features/sharing/view/widgets/file_thumbnail.dart';
import 'package:paperless_mobile/features/sharing/view/widgets/pdf_thumbail.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/routing/routes/labels_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';
import 'package:pdfx/pdfx.dart';

class DocumentUploadResult {
  final bool success;
  final String? taskId;

  DocumentUploadResult(this.success, this.taskId);
}

class DocumentUploadPreparationPage extends StatefulWidget {
  final FutureOr<Uint8List> fileBytes;
  final String? title;
  final String? filename;
  final String? fileExtension;

  const DocumentUploadPreparationPage({
    super.key,
    required this.fileBytes,
    this.title,
    this.filename,
    this.fileExtension,
  });

  @override
  State<DocumentUploadPreparationPage> createState() =>
      _DocumentUploadPreparationPageState();
}

class _DocumentUploadPreparationPageState
    extends State<DocumentUploadPreparationPage> {
  static const fkFileName = "filename";
  static final fileNameDateFormat = DateFormat("yyyy_MM_ddTHH_mm_ss");

  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  Map<String, String> _errors = {};
  late bool _syncTitleAndFilename;
  final bool _showDatePickerDeleteIcon = false;
  DateTime _now = DateTime.now();
  double _sizeInKB = 0;

  final TextEditingController _fileNameController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();
  late DocumentUploadCubit documentUploadCubit;

  @override
  void initState() {
    super.initState();
    _fileNameController.text = '${widget.title}${widget.fileExtension}';
    _titleController.text = widget.title ?? '';
    _syncTitleAndFilename = widget.filename == null && widget.title == null;
    _calculateFileSize();
    documentUploadCubit = context.read<DocumentUploadCubit>();
    documentUploadCubit.getUser();
    documentUploadCubit.getGroups();
    documentUploadCubit.getAllProject();
  }

  Future<void> _calculateFileSize() async {
    final bytes = await Future.value(widget.fileBytes);
    setState(() {
      _sizeInKB = bytes.lengthInBytes / 1024;
    });
  }

  @override
  Widget build(BuildContext context) {
    final DocumentUploadCubit documentUploadCubit =
        context.read<DocumentUploadCubit>();
    final labelRepository = context.watch<LabelRepository>();
    return BlocBuilder<DocumentUploadCubit, DocumentUploadState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColor.backgroundColor,
          appBar: AppBar(
            title: const Row(
              children: [
                Text('Add document', style: AppTextStyles.textStyleAppBar),
              ],
            ),
            iconTheme: const IconThemeData(color: AppColor.primary),
          ),
          extendBodyBehindAppBar: false,
          resizeToAvoidBottomInset: true,
          // floatingActionButton: Visibility(
          //   visible: MediaQuery.of(context).viewInsets.bottom == 0,
          //   child: FloatingActionButton.extended(
          //     heroTag: "fab_document_upload",
          //     onPressed: state.uploadProgress == null ? _onSubmit : null,
          //     label: state.uploadProgress == null
          //         ? Text(S.of(context)!.upload)
          //         : const Text("Uploading..."), //TODO: INTL
          //     icon: state.uploadProgress == null
          //         ? const Icon(Icons.upload)
          //         : SizedBox(
          //             height: 24,
          //             width: 24,
          //             child: CircularProgressIndicator(
          //               strokeWidth: 3,
          //               value: state.uploadProgress,
          //             )).padded(4),
          //   ),
          // ),
          body: FormBuilder(
            key: _formKey,
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: FutureOrBuilder<Uint8List>(
                    future: widget.fileBytes,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return const SizedBox.shrink();
                      }

                      final isPdf = widget.fileExtension
                              ?.toLowerCase()
                              .replaceAll('.', '') ==
                          'pdf';

                      logger.fd(
                        "File info - Extension: ${widget.fileExtension}, Is PDF: $isPdf, Data size: ${snapshot.data?.length ?? 0} bytes",
                        className: runtimeType.toString(),
                        methodName: "build",
                      );

                      return Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 24),
                        height: 168,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 24),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Colors.white,
                        ),
                        child: Row(
                          children: [
                            if (isPdf)
                              PdfThumbnail(
                                pdfData: snapshot.data!,
                                width: 120,
                                height: 120,
                              )
                            else
                              FileThumbnail(
                                bytes: snapshot.data!,
                                fit: BoxFit.fitWidth,
                                width: 120,
                                height: 120,
                              ),
                            const Gap(8),
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '${widget.title}${widget.fileExtension} ',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w700),
                                ),
                                const Gap(10),
                                Text('${_sizeInKB.toStringAsFixed(1)} KB'),
                              ],
                            ))
                          ],
                        ),
                      );
                    },
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Title',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        TextField(
                          controller: _titleController,
                          style: const TextStyle(fontWeight: FontWeight.w400),
                          decoration: InputDecoration(
                              fillColor: AppColor.white,
                              filled: true,
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 14, vertical: 0),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none)),
                        ),
                        const Gap(20),
                        const Text(
                          'File name',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        TextField(
                          controller: _fileNameController,
                          style: const TextStyle(fontWeight: FontWeight.w400),
                          decoration: InputDecoration(
                              fillColor: AppColor.white,
                              filled: true,
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 14, vertical: 0),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none)),
                        ),
                        const Gap(14),
                        SizedBox(
                            height: 46,
                            child: Row(
                              children: [
                                const Text(
                                  'Synchronize title and filename',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const Spacer(),
                                IntrinsicHeight(
                                  child: CupertinoSwitch(
                                    activeColor: AppColor.primary,
                                    value: _syncTitleAndFilename,
                                    onChanged: (bool value) {
                                      setState(() {
                                        setState(
                                          () => _syncTitleAndFilename = value,
                                        );
                                        if (_syncTitleAndFilename) {
                                          final String transformedValue =
                                              _formatFilename(_formKey
                                                  .currentState
                                                  ?.fields[
                                                      DocumentModel.titleKey]
                                                  ?.value as String);
                                          if (_syncTitleAndFilename) {
                                            _formKey.currentState
                                                ?.fields[fkFileName]
                                                ?.didChange(transformedValue);
                                          }
                                        }
                                      });
                                    },
                                  ),
                                ),
                              ],
                            )),
                        const Gap(20),
                        const Text(
                          'Created at*',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        Container(
                          height: 46,
                          padding: const EdgeInsets.symmetric(horizontal: 14),
                          alignment: Alignment.centerLeft,
                          decoration: BoxDecoration(
                              color: AppColor.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                DateFormat('MM/dd/yyyy').format(_now),
                              ),
                              GestureDetector(
                                  onTap: () async {
                                    final DateTime? selectedDate =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime(2050),
                                      initialEntryMode:
                                          DatePickerEntryMode.calendarOnly,
                                      builder: (context, child) => Theme(
                                        data: Theme.of(context).copyWith(
                                          colorScheme: const ColorScheme.light(
                                            background: AppColor.white,
                                            surfaceTint: Colors.transparent,
                                            primary: AppColor.primary,
                                            onPrimary: AppColor.white,
                                            // onSurface: AppColor.black,
                                          ),
                                        ),
                                        child: child!,
                                      ),
                                    );
                                    if (selectedDate != null) {
                                      setState(() {
                                        _now = selectedDate;
                                      });
                                    }
                                  },
                                  child: SvgPicture.asset(
                                      'assets/svgs/calendar.svg'))
                            ],
                          ),
                        ),
                        const Gap(20),
                        const Text(
                          'Correspondent*',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        if (context
                            .watch<LocalUserAccount>()
                            .paperlessUser
                            .canViewDocumentTypes)
                          Container(
                            height: 46,
                            // padding: const EdgeInsets.symmetric(horizontal: 14),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: LabelFormField<Correspondent>(
                              titleAppBar: S.of(context)!.correspondent,
                              showAnyAssignedOption: false,
                              showNotAssignedOption: false,
                              onAddLabel: (initialName) => CreateLabelRoute(
                                LabelType.correspondent,
                                name: initialName,
                              ).push<Correspondent>(context),
                              addLabelText: S.of(context)!.addCorrespondent,
                              labelText: S.of(context)!.correspondent,
                              name: DocumentModel.correspondentKey,
                              options: labelRepository.correspondents,
                              prefixIcon: const Icon(Icons.person_outline),
                              allowSelectUnassigned: true,
                              canCreateNewLabel: context
                                  .watch<LocalUserAccount>()
                                  .paperlessUser
                                  .canCreateCorrespondents,
                            ),
                          ),
                        const Gap(20),
                        const Text(
                          'Document type*',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        if (context
                            .watch<LocalUserAccount>()
                            .paperlessUser
                            .canViewDocumentTypes)
                          Container(
                            height: 46,
                            // padding: const EdgeInsets.symmetric(horizontal: 14),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: LabelFormField<DocumentType>(
                              titleAppBar: S.of(context)!.documentType,
                              showAnyAssignedOption: false,
                              showNotAssignedOption: false,
                              onAddLabel: (initialName) => CreateLabelRoute(
                                LabelType.documentType,
                                name: initialName,
                              ).push<DocumentType>(context),
                              addLabelText: S.of(context)!.addDocumentType,
                              labelText: S.of(context)!.documentType,
                              name: DocumentModel.documentTypeKey,
                              options: labelRepository.documentTypes,
                              prefixIcon:
                                  const Icon(Icons.description_outlined),
                              allowSelectUnassigned: true,
                              canCreateNewLabel: context
                                  .watch<LocalUserAccount>()
                                  .paperlessUser
                                  .canCreateDocumentTypes,
                            ),
                          ),
                        const Gap(20),
                        const Text(
                          'Tags',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        if (context
                            .watch<LocalUserAccount>()
                            .paperlessUser
                            .canViewDocumentTypes)
                          Container(
                            height: 46,
                            // padding: const EdgeInsets.symmetric(horizontal: 14),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: TagsFormField(
                              name: DocumentModel.tagsKey,
                              allowCreation: true,
                              allowExclude: false,
                              allowOnlySelection: true,
                              options: labelRepository.tags,
                            ),
                          ),
                        const Gap(20),
                        const Text(
                          'Permissions',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        GestureDetector(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PermissionsPage(
                                      documentUploadCubit: documentUploadCubit),
                                ));
                          },
                          child: Container(
                            height: 46,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'General access',
                                    style: AppTextStyles.textStyle14.copyWith(
                                        fontWeight: FontWeight.w500,
                                        color: AppColor.black_3C3C43
                                            .withOpacity(0.6)),
                                  ),
                                  const Icon(Icons.chevron_right)
                                ]),
                          ),
                        ),
                        const Gap(20),
                        const Text(
                          'Projects',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Gap(6),
                        GestureDetector(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ProjectPage(
                                      documentUploadCubit: documentUploadCubit),
                                ));
                          },
                          child: Container(
                            height: 46,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Select projects',
                                    style: AppTextStyles.textStyle14.copyWith(
                                        fontWeight: FontWeight.w500,
                                        color: AppColor.black_3C3C43
                                            .withOpacity(0.6)),
                                  ),
                                  const Icon(Icons.chevron_right)
                                ]),
                          ),
                        ),
                        const Gap(30),
                        const Text(
                            '* If you specify values for these fields, your Papierlos instance will not automatically derive a value. If you want these values to be automatically populated by your server, leave the fields blank.'),
                        const Gap(48),
                        GestureDetector(
                          onTap: () {
                            _onSubmit();
                          },
                          child: Container(
                            height: 48,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: AppColor.primary),
                            child: const Text(
                              'Upload',
                              style: TextStyle(
                                  color: AppColor.white,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                        const Gap(30),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onSubmit() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final cubit = context.read<DocumentUploadCubit>();
      try {
        final formValues = _formKey.currentState!.value;

        final correspondentParam =
            formValues[DocumentModel.correspondentKey] as IdQueryParameter?;
        final docTypeParam =
            formValues[DocumentModel.documentTypeKey] as IdQueryParameter?;
        final tagsParam = formValues[DocumentModel.tagsKey] as TagsQuery?;
        final createdAt = formValues[DocumentModel.createdKey] as FormDateTime?;
        final title = formValues[DocumentModel.titleKey] as String? ??
            widget.title ??
            "scan_${fileNameDateFormat.format(_now)}";
        final correspondent = switch (correspondentParam) {
          SetIdQueryParameter(id: var id) => id,
          _ => null,
        };
        final docType = switch (docTypeParam) {
          SetIdQueryParameter(id: var id) => id,
          _ => null,
        };
        final tags = switch (tagsParam) {
          IdsTagsQuery(include: var ids) => ids,
          _ => const <int>[],
        };

        final asn = formValues[DocumentModel.asnKey] as int?;
        final taskId = await cubit.upload(
          await widget.fileBytes,
          filename: _padWithExtension(
            _formKey.currentState?.value[fkFileName] as String? ??
                widget.filename ??
                "scan_${fileNameDateFormat.format(_now)}",
            widget.fileExtension,
          ),
          userId: Hive.box<GlobalSettings>(HiveBoxes.globalSettings)
              .getValue()!
              .loggedInUserId!,
          title: title,
          documentType: docType,
          correspondent: correspondent,
          tags: tags,
          createdAt: createdAt?.toDateTime(),
          asn: asn,
        );
        // print(object)
        // ignore: use_build_context_synchronously
        showSnackBar(
          context,
          S.of(context)!.documentSuccessfullyUploadedProcessing,
        );
        context.pop(DocumentUploadResult(true, taskId));
      } on PaperlessFormValidationException catch (exception) {
        setState(() => _errors = exception.validationMessages);
      } catch (error, stackTrace) {
        logger.fe(
          "An unknown error occurred during document upload.",
          className: runtimeType.toString(),
          methodName: "_onSubmit",
          error: error,
          stackTrace: stackTrace,
        );
        showErrorMessage(
          context,
          const PaperlessApiException.unknown(),
          stackTrace,
        );
      }
    }
  }

  String _padWithExtension(String source, [String? extension]) {
    final ext = extension ?? '.pdf';
    return source.endsWith(ext) ? source : '$source$ext';
  }

  String _formatFilename(String source) {
    return source.replaceAll(RegExp(r"[\W_]"), "_").toLowerCase();
  }

  // Future<Color> _computeAverageColor() async {
  //   final bitmap = img.decodeImage(await widget.fileBytes);
  //   if (bitmap == null) {
  //     return Colors.black;
  //   }
  //   int redBucket = 0;
  //   int greenBucket = 0;
  //   int blueBucket = 0;
  //   int pixelCount = 0;

  //   for (int y = 0; y < bitmap.height; y++) {
  //     for (int x = 0; x < bitmap.width; x++) {
  //       final c = bitmap.getPixel(x, y);

  //       pixelCount++;
  //       redBucket += c.r.toInt();
  //       greenBucket += c.g.toInt();
  //       blueBucket += c.b.toInt();
  //     }
  //   }

  //   return Color.fromRGBO(
  //     redBucket ~/ pixelCount,
  //     greenBucket ~/ pixelCount,
  //     blueBucket ~/ pixelCount,
  //     1,
  //   );
  // }

  Widget _buildItemTitleAndWidget(
      {required String title,
      required Widget child,
      bool isShowBorder = true}) {
    return Container(
        height: 46,
        decoration: isShowBorder
            ? const BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        color: AppColor.grey_909090, style: BorderStyle.solid)))
            : null,
        child: Row(
          children: [
            const Gap(14),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            IntrinsicHeight(child: child),
            const Gap(14),
          ],
        ));
  }
}
